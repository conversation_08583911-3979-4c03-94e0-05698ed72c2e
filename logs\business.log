2025-07-30 17:43:37 - business - INFO - BUSINESS_OP: {"operation": "test_operation", "timestamp": "2025-07-30T17:43:37.180503", "user_id": "test_user", "session_id": "test_session", "ip_address": "127.0.0.1", "user_agent": "python-requests/2.31.0", "details": {"test_field": "test_value", "timestamp": 1753868616.1593199}}
2025-07-30 17:43:38 - business - ERROR - ERROR_OP: {"operation": "test_error_operation", "error": "This is a test error", "timestamp": "2025-07-30T17:43:38.187680", "user_id": "test_user", "session_id": "test_session", "ip_address": "127.0.0.1", "user_agent": "python-requests/2.31.0", "details": {"error": "This is a test error", "stack": "Test stack trace"}}
2025-07-30 17:43:39 - business - INFO - USER_ACTION: {"action": "test_user_action", "timestamp": "2025-07-30T17:43:39.193860", "user_id": "test_user", "session_id": "test_session", "ip_address": "127.0.0.1", "details": {"action_type": "click", "element": "test_button"}}
