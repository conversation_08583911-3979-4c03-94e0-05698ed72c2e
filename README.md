# 8D问题分析报告生成系统

这是一个专业的8D（Eight Disciplines）问题分析报告生成系统，基于Flask开发，提供Web界面和API服务，能够自动生成标准格式的8D分析报告PPT和DOCX文档。

## 🎯 系统概述

8D是一种用于产品和过程改进的问题解决方法，广泛应用于质量管理领域。本系统提供：

- 📝 **智能Web表单**：引导式8D报告填写界面，包含完整的8个步骤
- 📊 **实时导航**：可视化进度追踪和章节跳转
- 📄 **文档生成**：自动生成专业的PPT演示文稿和DOCX报告
- 🔄 **模板管理**：支持多版本模板和自定义格式
- 💾 **报告存档**：自动保存填写记录，支持历史查询

## 🚀 功能特点

### Web界面功能
- ✅ **交互式表单**：专业的8D报告填写界面，包含使用说明
- ✅ **实时导航**：动态显示当前章节，支持点击跳转
- ✅ **进度追踪**：显示各步骤完成状态和整体进度
- ✅ **数据验证**：智能表单验证，确保必填项完整性
- ✅ **响应式设计**：支持桌面和移动设备访问
- ✅ **语音输入**：支持语音转文字，提高填写效率

### 文档生成功能
- ✅ **双格式输出**：同时生成PPT和DOCX格式报告
- ✅ **模板替换**：智能识别和替换文档中的占位符
- ✅ **格式保持**：保持原有字体、颜色、样式等格式
- ✅ **嵌套数据支持**：处理复杂的团队成员、原因分析等结构化数据
- ✅ **自动清理**：删除未使用的占位符，保证文档整洁

### 8D步骤支持
| 步骤 | 内容 | 系统支持 |
|------|------|----------|
| D0 | 征兆紧急反应措施 | ✅ 问题描述、紧急措施记录 |
| D1 | 成立改善小组 | ✅ 团队成员管理、角色分配 |
| D2 | 问题说明 | ✅ 问题详细描述、影响分析 |
| D3 | 实施并验证临时措施 | ✅ 临时措施记录、验证结果 |
| D4 | 确定并验证根本原因 | ✅ 根本原因分析、验证方法 |
| D5 | 选择并验证永久纠正措施 | ✅ 纠正措施选择、验证计划 |
| D6 | 实施永久纠正措施 | ✅ 实施计划、责任分配 |
| D7 | 预防再发生 | ✅ 预防措施、系统改进 |
| D8 | 恭喜小组 | ✅ 团队表彰、经验总结 |

## 🏗️ 项目结构

```
8d_ppt_docx/
├── app.py                    # Flask主应用
├── requirements.txt          # Python依赖包
├── README.md                # 项目说明文档
│
├── templates/               # HTML模板
│   ├── d8_form.html        # 8D报告填写表单
│   └── download.html       # 文件下载页面
│
├── static/                 # 静态资源
│   ├── css/
│   │   ├── d8_form.css     # 表单样式（紫色主题）
│   │   └── download.css    # 下载页面样式
│   └── js/
│       ├── d8_form.js      # 表单交互逻辑
│       └── download.js     # 下载页面逻辑
│
├── 8d_template/            # 8D报告模板
│   ├── 0530/              # 5月30日版本模板
│   └── 0610/              # 6月10日版本模板
│       ├── template.docx   # DOCX模板
│       └── template.pptx   # PPT模板
│
├── 8d_reports/             # 生成的8D报告JSON记录
├── generated_docx/         # 生成的DOCX文件
├── generated_ppts/         # 生成的PPT文件
│
├── test_docx_service.py    # DOCX服务测试
├── test_pptx_service.py    # PPT服务测试
└── file_mapping.pkl        # 文件映射缓存
```

## 📦 安装和运行

### 环境要求
- Python 3.7+
- Flask 3.0.2
- python-pptx 0.6.23
- python-docx 1.1.0

### 快速开始

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **启动服务**
```bash
python app.py
```

3. **访问系统**
```
Web界面: http://localhost:5555
API服务: http://localhost:5555/api
```

## 🖥️ Web界面使用

### 访问8D报告填写表单
```
http://localhost:5555
```

### 功能说明

1. **使用说明**：点击页面顶部的使用说明，了解8D方法和填写指导
2. **导航栏**：实时显示当前步骤，点击可跳转到对应章节
3. **表单填写**：按照8D步骤逐步填写各项内容
4. **团队管理**：动态添加/删除团队成员信息
5. **进度追踪**：查看各步骤完成状态和整体进度
6. **语音输入**：点击麦克风图标启用语音转文字功能
7. **生成报告**：完成填写后点击生成，自动创建PPT和DOCX文档

### 界面特色

- 🎨 **现代化设计**：采用渐变紫色主题，专业美观
- 🧭 **智能导航**：实时高亮当前章节，支持快速跳转
- 📱 **响应式布局**：适配各种屏幕尺寸
- ⚡ **实时反馈**：表单验证和进度显示
- 🎯 **用户友好**：清晰的步骤指引和提示信息

## 📖 API接口文档

### 1. 生成8D报告

#### 请求信息
```
POST /generate_8d_report
Content-Type: application/json
```

#### 请求体示例
```json
{
    "d0_problem_description": "产品外观缺陷问题",
    "d0_emergency_measures": "立即停止生产，隔离问题产品",
    "d1_team_members": [
        {
            "name": "张三",
            "department": "质量部",
            "role": "组长",
            "contact": "<EMAIL>"
        }
    ],
    "d2_problem_statement": "产品表面出现划痕，影响外观质量",
    "d3_temporary_measures": "增加检验工序，加强包装保护",
    "d4_root_cause": "包装材料粗糙导致运输过程中划伤",
    "d5_corrective_actions": "更换防护性更好的包装材料",
    "d6_implementation_plan": "2024年4月1日前完成包装材料更换",
    "d7_prevention_measures": "建立包装材料验收标准",
    "d8_team_recognition": "感谢团队快速响应和有效解决问题"
}
```

#### 响应格式
```json
{
    "status": "success",
    "message": "8D报告生成成功",
    "files": {
        "ppt": "http://localhost:5555/download/uuid-ppt-id",
        "docx": "http://localhost:5555/download/uuid-docx-id"
    },
    "report_id": "8d_report_20240321_153000_abc123"
}
```

### 2. 查询历史报告

#### 请求信息
```
GET /8d_reports
GET /8d_reports?date=2024-03-21
```

#### 响应格式
```json
{
    "reports": [
        {
            "id": "8d_report_20240321_153000_abc123",
            "created_at": "2024-03-21 15:30:00",
            "problem_description": "产品外观缺陷问题",
            "status": "completed"
        }
    ]
}
```

### 3. 文件下载

#### 下载页面
```
GET /download/<download_id>
```
显示美观的下载页面，包含文件信息和下载按钮。

#### 直接下载
```
GET /download_file/<download_id>
```
直接下载文件，适用于程序调用。

## 🔧 模板自定义

### 模板占位符规范

在PPT和DOCX模板中使用以下占位符：

#### 基础信息
```
${problem_title}           # 问题标题
${report_date}            # 报告日期
${department}             # 部门
${product_name}           # 产品名称
```

#### D0-D8步骤
```
${d0_problem_description}  # D0问题描述
${d0_emergency_measures}   # D0紧急措施
${d1_team_leader}         # D1组长
${d1_team_members}        # D1团队成员
${d2_problem_statement}   # D2问题说明
${d3_temporary_measures}  # D3临时措施
${d4_root_cause}          # D4根本原因
${d5_corrective_actions}  # D5纠正措施
${d6_implementation_plan} # D6实施计划
${d7_prevention_measures} # D7预防措施
${d8_team_recognition}    # D8团队表彰
```

#### 团队信息
```
${team_member_1_name}     # 第1个成员姓名
${team_member_1_dept}     # 第1个成员部门
${team_member_1_role}     # 第1个成员角色
...
```

### 新增模板版本

1. 在 `8d_template/` 下创建新的日期文件夹（如 `0701/`）
2. 放入新的 `template.pptx` 和 `template.docx`
3. 在模板中使用上述占位符
4. 重启服务生效

## 🧪 测试和调试

### 运行测试

```bash
# 测试PPT生成服务
python test_pptx_service.py

# 测试DOCX生成服务
python test_docx_service.py
```

### 查看日志

```bash
# 查看最新日志
tail -f 日志_*.log

# 查看特定日期日志
cat 日志_0610.log
```

### 调试技巧

1. **检查生成文件**
   ```bash
   ls -la generated_ppts/
   ls -la generated_docx/
ls -la 8d_reports/
```

2. **验证模板占位符**
   - 打开模板文件
   - 搜索 `${` 确认占位符格式正确
   - 确保占位符名称与代码中一致

3. **测试Web界面**
   - 使用浏览器开发者工具检查网络请求
   - 查看控制台错误信息
   - 验证表单数据格式

## ⚙️ 配置和部署

### 环境变量
```bash
# 服务端口（默认5555）
export PORT=5555

# 模板版本（默认0610）
export TEMPLATE_VERSION=0610

# 文件过期时间（默认7天）
export FILE_EXPIRE_DAYS=7
```

### 生产环境部署

1. **使用Gunicorn**
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5555 app:app
```

2. **使用Docker**
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5555
CMD ["python", "app.py"]
```

3. **Nginx反向代理**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:5555;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📋 使用建议

### 最佳实践

1. **模板设计**
   - 保持占位符命名规范和一致性
   - 使用有意义的占位符名称
   - 确保模板格式专业美观

2. **数据填写**
   - 遵循8D方法论，确保各步骤逻辑性
   - 提供具体、可量化的信息
   - 及时保存和备份重要数据

3. **系统维护**
   - 定期清理过期的生成文件
   - 备份重要的报告记录
   - 监控磁盘空间使用情况

### 常见问题解决

#### 1. 模板占位符未替换
- 检查占位符格式是否为 `${variable_name}`
- 确认变量名称拼写正确
- 查看日志文件了解具体错误

#### 2. 生成的文档格式异常
- 验证原始模板文件完整性
- 检查数据中是否包含特殊字符
- 尝试使用简化的测试数据

#### 3. Web界面加载缓慢
- 检查网络连接状态
- 清除浏览器缓存
- 确认服务器性能是否正常

## 🔄 版本历史

### v4.0 (当前版本)
- 🆕 新增专业的8D报告Web界面
- 🆕 智能导航和进度追踪功能
- 🆕 语音输入和响应式设计
- 🆕 8D报告JSON记录存储
- 🆕 历史报告查询功能

### v3.0
- ✅ 组合形状和图表文本支持
- ✅ 增强的文档处理能力
- ✅ 详细的处理日志记录

### v2.0
- ✅ DOCX文档生成支持
- ✅ 嵌套数据结构处理
- ✅ 文件下载管理优化

### v1.0
- ✅ 基础PPT模板替换功能
- ✅ Flask API服务框架

## 🤝 贡献和支持

### 贡献代码
1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 问题反馈
- 🐛 Bug报告：请详细描述复现步骤
- 💡 功能建议：欢迎提出改进意见
- 📝 文档改进：帮助完善使用说明

### 技术支持
- 📧 邮件：<EMAIL>
- 💬 Issues：GitHub Issues页面
- 📞 电话：技术支持热线

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

**8D问题分析报告生成系统** - 让质量改进更高效、更专业！

*最后更新：2024年7月* 