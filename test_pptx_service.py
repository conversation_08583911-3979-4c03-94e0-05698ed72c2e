"""
@Description : Do something
<AUTHOR> <PERSON>
@Copyright   : 浙江求是半导体设备有限公司
@Date        : 2025-05-30 10:31:14
@LastEditors : <PERSON>
@LastEditTime: 2025-05-30 13:24:22
@Modified    : <PERSON>
"""
import requests
import json
import os


def test_ppt_service():
    # 服务地址
    base_url = 'http://localhost:5555'
    process_url = f'{base_url}/process_ppt'
    
    # 设置请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        # 读取example.json文件
        print("正在读取example.json文件...")
        with open('example.json', 'r', encoding='utf-8') as f:
            content = f.read()
            try:
                data = json.loads(content)
                print("JSON文件解析成功！")
            except json.JSONDecodeError as je:
                print(f"JSON解析错误：{str(je)}")
                print(f"错误位置：行 {je.lineno}, 列 {je.colno}")
                print(f"错误内容：{je.msg}")
                return
            
        # 确保template.pptx文件存在
        if not os.path.exists(data['template_name']):
            print(f"错误：模板文件 {data['template_name']} 不存在！")
            return
            
        # 发送POST请求生成PPT
        print("正在发送请求生成PPT...")
        response = requests.post(process_url, json=data, headers=headers)
        
        # 检查响应状态
        if response.status_code == 200:
            response_data = response.json()
            if 'download_url' in response_data:
                print("PPT生成成功！")
                print(f"下载链接：{response_data['download_url']}")
                print("提示：您可以在浏览器中打开此链接来下载文件")
            else:
                print("错误：服务器响应中没有下载链接")
        else:
            # 打印错误信息
            try:
                error_msg = response.json()
                print(f"错误：{error_msg.get('error', '未知错误')}")
            except json.JSONDecodeError:
                print(f"错误：服务器返回非JSON格式响应：{response.text}")
            print(f"状态码：{response.status_code}")
            
    except FileNotFoundError:
        print("错误：找不到example.json文件！")
        print(f"当前工作目录：{os.getcwd()}")
    except requests.exceptions.ConnectionError:
        print("错误：无法连接到服务器，请确保服务正在运行！")
    except Exception as e:
        print(f"发生错误：{str(e)}")
        print(f"错误类型：{type(e).__name__}")


if __name__ == "__main__":
    test_ppt_service() 